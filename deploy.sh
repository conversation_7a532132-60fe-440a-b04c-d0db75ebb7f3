#!/bin/bash

# ETender Multi-Stack Deployment Script
# This script builds a Docker image and deploys it to AWS using multiple CloudFormation stacks

# Exit on any error
set -e

# Configuration variables
ECR_REPOSITORY_URI="881490112356.dkr.ecr.ap-south-1.amazonaws.com/nchac/etender-laravel"
ECS_CLUSTER_NAME="etender-cluster"
VPC_ID="vpc-0adb6f6ede3ba08a6"
SUBNETS="subnet-09bf79ee94abed510,subnet-0c1df04797e279bf8"
BASE_STACK_NAME="etender"
AWS_REGION="ap-south-1"
DB_NAME="etender"
DB_USERNAME="etender"
DB_PASSWORD=""
ENVIRONMENT="prod"
ACM_CERTIFICATE_ARN="arn:aws:acm:ap-south-1:881490112356:certificate/c9a86a45-c382-4613-ac38-a5ff3eb983e8"

# Parse command line arguments
BRANCH_NAME="master"
IMAGE_TAG=$(git rev-parse --short HEAD)
SKIP_DB_STACK="false"
SKIP_NETWORK_STACK="false"
SKIP_APP_STACK="false"

# Get current deployment version and increment it
DB_STACK_NAME="${BASE_STACK_NAME}-db-${ENVIRONMENT}"
NETWORK_STACK_NAME="${BASE_STACK_NAME}-network-${ENVIRONMENT}"
APP_STACK_NAME="${BASE_STACK_NAME}-app-${ENVIRONMENT}"

CURRENT_VERSION=$(aws cloudformation describe-stacks --profile nchac --stack-name $APP_STACK_NAME --region $AWS_REGION --query "Stacks[0].Parameters[?ParameterKey=='DeploymentVersion'].ParameterValue" --output text 2>/dev/null || echo "v0")
if [[ $CURRENT_VERSION == "v0" ]]; then
  DEPLOYMENT_VERSION="v1"
elif [[ $CURRENT_VERSION =~ ^v([0-9]+)$ ]]; then
  NEXT_VERSION=$((${BASH_REMATCH[1]} + 1))
  DEPLOYMENT_VERSION="v$NEXT_VERSION"
else
  # Fallback to timestamp if we can't determine the version
  DEPLOYMENT_VERSION="v$(date +%Y%m%d%H%M%S)"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --branch)
      BRANCH_NAME="$2"
      shift 2
      ;;
    --image-tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --db-password)
      DB_PASSWORD="$2"
      shift 2
      ;;
    --environment)
      ENVIRONMENT="$2"
      # Update stack names based on environment
      DB_STACK_NAME="${BASE_STACK_NAME}-db-${ENVIRONMENT}"
      NETWORK_STACK_NAME="${BASE_STACK_NAME}-network-${ENVIRONMENT}"
      APP_STACK_NAME="${BASE_STACK_NAME}-app-${ENVIRONMENT}"
      shift 2
      ;;
    --skip-db)
      SKIP_DB_STACK="true"
      shift
      ;;
    --skip-network)
      SKIP_NETWORK_STACK="true"
      shift
      ;;
    --skip-app)
      SKIP_APP_STACK="true"
      shift
      ;;
    --help)
      echo "Usage: $0 [--branch BRANCH_NAME] [--image-tag IMAGE_TAG] [--db-password PASSWORD] [--environment ENV] [--skip-db] [--skip-network] [--skip-app]"
      echo ""
      echo "Options:"
      echo "  --branch       The branch name for environment configuration (default: master)"
      echo "  --image-tag    The Docker image tag (default: git commit hash)"
      echo "  --db-password  The database password (required for first DB deployment)"
      echo "  --environment  Deployment environment (default: prod)"
      echo "  --skip-db      Skip database stack deployment"
      echo "  --skip-network Skip network stack deployment"
      echo "  --skip-app     Skip application stack deployment"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Check if DB password is provided for first deployment
if [[ "$SKIP_DB_STACK" == "false" && -z "$DB_PASSWORD" ]]; then
  # Check if DB stack already exists
  if ! aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --region $AWS_REGION --profile nchac &>/dev/null; then
    echo "❌ Error: --db-password is required for first database deployment"
    echo "Usage: $0 [--branch BRANCH_NAME] [--image-tag IMAGE_TAG] --db-password PASSWORD"
    exit 1
  fi
fi

echo "🚀 Starting multi-stack deployment process..."
echo "Branch: $BRANCH_NAME"
echo "Image tag: $IMAGE_TAG"
echo "Environment: $ENVIRONMENT"
echo "Deployment version: $DEPLOYMENT_VERSION (incremented from $CURRENT_VERSION)"

# Build Docker image if we're deploying the app stack
if [[ "$SKIP_APP_STACK" == "false" ]]; then
  echo "📦 Building Docker image..."
  docker build -t $ECR_REPOSITORY_URI:$IMAGE_TAG .

  # Authenticate with AWS ECR
  echo "🔑 Authenticating with AWS ECR..."
  aws ecr get-login-password --region $AWS_REGION --profile nchac | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI

  # Push Docker image to ECR
  echo "⬆️ Pushing Docker image to ECR..."
  docker push $ECR_REPOSITORY_URI:$IMAGE_TAG
fi

# Deploy Database Stack if not skipped
if [[ "$SKIP_DB_STACK" == "false" ]]; then
  echo "🗄️ Deploying Database Stack..."

  DB_PARAMS="VPCId=$VPC_ID Subnets=$SUBNETS DBName=$DB_NAME DBUsername=$DB_USERNAME EnvironmentName=$ENVIRONMENT"

  # Add DB password if provided
  if [[ -n "$DB_PASSWORD" ]]; then
    DB_PARAMS="$DB_PARAMS DBPassword=$DB_PASSWORD"
  fi

  aws cloudformation deploy \
    --template-file database-stack.yaml \
    --stack-name $DB_STACK_NAME \
    --parameter-overrides $DB_PARAMS \
    --capabilities CAPABILITY_IAM \
    --region $AWS_REGION \
    --profile nchac \
    --no-fail-on-empty-changeset

  # Get Database outputs for next stacks
  DB_ENDPOINT=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='DatabaseEndpoint'].OutputValue" --output text --region $AWS_REGION --profile nchac)
  DB_PORT=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='DatabasePort'].OutputValue" --output text --region $AWS_REGION --profile nchac)
  DB_SG_ID=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='DatabaseSecurityGroupId'].OutputValue" --output text --region $AWS_REGION --profile nchac)

  echo "✅ Database Stack deployed successfully!"
  echo "Database Endpoint: $DB_ENDPOINT"
  echo "Database Port: $DB_PORT"
else
  # If skipping DB stack, get existing values
  DB_ENDPOINT=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='DatabaseEndpoint'].OutputValue" --output text --region $AWS_REGION --profile nchac)
  DB_PORT=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='DatabasePort'].OutputValue" --output text --region $AWS_REGION --profile nchac)
  DB_SG_ID=$(aws cloudformation describe-stacks --stack-name $DB_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='DatabaseSecurityGroupId'].OutputValue" --output text --region $AWS_REGION --profile nchac)

  echo "ℹ️ Using existing Database Stack"
  echo "Database Endpoint: $DB_ENDPOINT"
  echo "Database Port: $DB_PORT"
fi

# Deploy Network Stack if not skipped
if [[ "$SKIP_NETWORK_STACK" == "false" ]]; then
  echo "🌐 Deploying Network Stack..."

  NETWORK_PARAMS="VPCId=$VPC_ID Subnets=$SUBNETS DBSecurityGroupId=$DB_SG_ID EnvironmentName=$ENVIRONMENT ACMCertificateArn=$ACM_CERTIFICATE_ARN"

  aws cloudformation deploy \
    --template-file network-stack.yaml \
    --stack-name $NETWORK_STACK_NAME \
    --parameter-overrides $NETWORK_PARAMS \
    --capabilities CAPABILITY_IAM \
    --region $AWS_REGION \
    --profile nchac \
    --no-fail-on-empty-changeset

  # Get Network outputs for app stack
  TARGET_GROUP_ARN=$(aws cloudformation describe-stacks --stack-name $NETWORK_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='TargetGroupARN'].OutputValue" --output text --region $AWS_REGION --profile nchac)
  CONTAINER_SG_ID=$(aws cloudformation describe-stacks --stack-name $NETWORK_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='ContainerSecurityGroupId'].OutputValue" --output text --region $AWS_REGION --profile nchac)

  echo "✅ Network Stack deployed successfully!"
else
  # If skipping Network stack, get existing values
  TARGET_GROUP_ARN=$(aws cloudformation describe-stacks --stack-name $NETWORK_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='TargetGroupARN'].OutputValue" --output text --region $AWS_REGION --profile nchac)
  CONTAINER_SG_ID=$(aws cloudformation describe-stacks --stack-name $NETWORK_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='ContainerSecurityGroupId'].OutputValue" --output text --region $AWS_REGION --profile nchac)

  echo "ℹ️ Using existing Network Stack"
fi

# Deploy Application Stack if not skipped
if [[ "$SKIP_APP_STACK" == "false" ]]; then
  echo "📱 Deploying Application Stack..."

  APP_PARAMS="DeploymentVersion=$DEPLOYMENT_VERSION BranchName=$BRANCH_NAME VPCId=$VPC_ID Subnets=$SUBNETS ImageTag=$IMAGE_TAG ExistingECRRepositoryUri=$ECR_REPOSITORY_URI ExistingECSClusterName=$ECS_CLUSTER_NAME DBName=$DB_NAME DBUsername=$DB_USERNAME DBPassword=$DB_PASSWORD DatabaseEndpoint=$DB_ENDPOINT DatabasePort=$DB_PORT TargetGroupARN=$TARGET_GROUP_ARN ContainerSecurityGroupId=$CONTAINER_SG_ID EnvironmentName=$ENVIRONMENT"

  aws cloudformation deploy \
    --template-file app-stack.yaml \
    --stack-name $APP_STACK_NAME \
    --parameter-overrides $APP_PARAMS \
    --capabilities CAPABILITY_IAM \
    --region $AWS_REGION \
    --profile nchac \
    --no-fail-on-empty-changeset

  echo "✅ Application Stack deployed successfully!"
else
  echo "ℹ️ Skipping Application Stack deployment"
fi

# Get the final deployment details
echo "📋 Deployment Summary:"

# Get Load Balancer DNS from Network Stack
LB_DNS=$(aws cloudformation describe-stacks --stack-name $NETWORK_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='LoadBalancerDNS'].OutputValue" --output text --region $AWS_REGION --profile nchac)
echo "Application URL: https://$LB_DNS"

# Get Service details if app was deployed
if [[ "$SKIP_APP_STACK" == "false" ]]; then
  SERVICE_NAME=$(aws cloudformation describe-stacks --stack-name $APP_STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='ServiceName'].OutputValue" --output text --region $AWS_REGION --profile nchac)
  echo "ECS Service: $SERVICE_NAME"
  echo "Service console: https://$AWS_REGION.console.aws.amazon.com/ecs/home?region=$AWS_REGION#/clusters/$ECS_CLUSTER_NAME/services/$SERVICE_NAME/details"
fi

echo "✅ Multi-stack deployment completed successfully!"
echo ""
echo "Stack Details:"
echo "📊 Database Stack: $DB_STACK_NAME"
echo "🌐 Network Stack: $NETWORK_STACK_NAME"
echo "📱 Application Stack: $APP_STACK_NAME"
echo ""
echo "🔍 Post-Deployment Verification:"
echo "1. Check ECS logs for 'Verifying application configuration...' output"
echo "2. Verify no 'ERROR Failed to clear cache' messages in logs"
echo "3. Test file uploads - should work without 401 Unauthorized errors"
echo "4. Check that session authentication is working properly"
echo ""
echo "Note: If this is your first deployment, you may want to verify your application's environment variables."